import React from 'react';
import Image from 'next/image';

interface PoweredByTerangProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'light' | 'dark';
}

export default function PoweredByTerang({ 
  className = '',
  size = 'lg',
  variant = 'light'
}: PoweredByTerangProps) {
  const sizeClasses = {
    sm: 'text-xs gap-1.5',
    md: 'text-sm gap-2',
    lg: 'text-base gap-2.5'
  };

  const logoSizes = {
    sm: { width: 60, height: 20 },
    md: { width: 80, height: 26 },
    lg: { width: 200, height: 33 }
  };

  const textClasses = {
    light: 'text-white',
    dark: 'text-white'
  };

  return (
    <div className={`flex items-center justify-center ${sizeClasses[size]} ${className}`}>
      <span className={`font-large ${textClasses[variant]}`}>
        Powered by
      </span>
      <Image
        src="https://cdn.terang.ai/images/logo/logo-terang-ai-combined.png"
        alt="Terang AI"
        width={logoSizes[size].width}
        height={logoSizes[size].height}
        className="object-contain"
        priority={false}
      />
    </div>
  );
}