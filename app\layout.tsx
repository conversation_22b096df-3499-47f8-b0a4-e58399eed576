import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Quicksand } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { cn } from "@/lib/utils";

const inter = Inter({ subsets: ["latin"], variable: "--font-sans" });
const quicksand = Quicksand({ 
  subsets: ["latin"], 
  variable: "--font-quicksand",
  weight: ["300", "400", "500", "600", "700"]
});

export const metadata: Metadata = {
  title: "KinderLib",
  description: "Fun stories for every kid - Read, play, and learn together",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          inter.variable,
          quicksand.variable
        )}
      >
        {children}  {/* No navbar here! */}
        <Toaster />
      </body>
    </html>
  );
}