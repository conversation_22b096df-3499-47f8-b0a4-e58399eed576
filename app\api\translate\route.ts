// app/api/translate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
if (!GEMINI_API_KEY) {
  throw new Error('Missing GEMINI_API_KEY environment variable');
}
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });

export async function POST(req: NextRequest) {
  try {
    const { blocks, targetLang }: { blocks: { id: number; text: string }[]; targetLang: string } = await req.json();
    if (!Array.isArray(blocks) || blocks.length === 0)
      return NextResponse.json({ error: 'Missing blocks' }, { status: 400 });

    const prompt = `
Translate the following Indonesian texts to ${targetLang}.
Return ONLY a valid JSON array with the same order and ids, each object containing:
{ "id": <original-id>, "translated": "<translated-text>" }

Input:
${JSON.stringify(blocks)}
`;
    console.log("Prompt Input : ",JSON.stringify(blocks));
    const result = await model.generateContent(prompt);
    const raw = result.response.text() ?? '';
    const cleaned = raw.replace(/```(?:json)?/gi, '').trim();
    const translated: { id: number; translated: string }[] = JSON.parse(cleaned);
    console.log('Gemini response:', translated);
    return NextResponse.json({ translated });
  } catch (e: any) {
    console.error('Gemini error:', e);
    return NextResponse.json({ error: e.message || 'Translation failed' }, { status: 500 });
  }
}