'use client';

import React, { useState, useEffect } from 'react';
import BookCard from '@/components/landingpage/BookCard';
import Pagination from '@/components/landingpage/Pagination';
import { BookAdjusted } from '@/lib/database/books';
import KidsPDFViewer from '@/components/KidsPdfViewer';

interface AvailableBooksProps {
  booksPerPage?: number;
}

export default function AvailableBooks({ booksPerPage = 3 }: AvailableBooksProps) {
  const [books, setBooks] = useState<BookAdjusted[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBook, setSelectedBook] = useState<BookAdjusted | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('id');
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/books');
      if (!response.ok) {
        throw new Error('Failed to fetch books');
      }
      const data = await response.json();
      setBooks(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(books.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = books.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    document.getElementById('books')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handlePreview = (bookId: number, language: string = 'id') => {
    const book = books.find(b => b.id === bookId);
    if (book) {
      setSelectedBook(book);
      setSelectedLanguage(language);
      setShowPDFViewer(true);
    }
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  const handleSubscription = (bookId: number) => {
  };

  const handleDownloadPreview = (bookId: number, language: string = 'id') => {
    const book = books.find(b => b.id === bookId);
    if (book) {
      let pdfUrl = book.originalPdfUrl;
      
      if (language !== 'id') {
        const languageVersion = book.languages.find(lang => lang.language === language);
        pdfUrl = languageVersion?.translatedPdfUrl || book.originalPdfUrl;
      }
      
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `${book.namaBuku}-${language}-preview.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  const getCurrentPdfUrl = () => {
    if (!selectedBook) return '';
    
    if (selectedLanguage === 'id') {
      return selectedBook.originalPdfUrl;
    }
    
    const languageVersion = selectedBook.languages.find(lang => lang.language === selectedLanguage);
    return languageVersion?.translatedPdfUrl || selectedBook.originalPdfUrl;
  };

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 py-8">
        <section id="books" className="space-y-6">
          {currentBooks.map((book) => (
            <BookCard
              key={book.id}
              book={book}
              onStartReading={handlePreview}
              onSubscription={handleSubscription}
              onDownloadPreview={handleDownloadPreview}
              isOwned={false}
            />
          ))}
        </section>

        {totalPages > 1 && (
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* KidsPDFViewer Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={getCurrentPdfUrl()}
          bookTitle={selectedBook.namaBuku}
          onClose={handleClosePDFViewer}
        />
      )}
    </>
  );
}