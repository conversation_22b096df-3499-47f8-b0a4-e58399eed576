// types/pdf.ts
export interface TextBlock {
  id: number;
  text: string;
  x: number;
  y: number;
  size: number;
  fontFamily: string;      // pastikan required
  fontWeight: string;      // required
  italic: boolean;         // required
  rotation: number;
  color: { r: number; g: number; b: number };
  translated?: string;     // opsional (hanya ada di translator)
  pageNumber: number;
  width: number;
  font?: string;           // opsional (hanya ada di translator)
  lineHeight?: number;
}