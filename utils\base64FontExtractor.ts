interface FontData {
  fontFamily: string;
  base64Data: string;
  format: string;
}

export class Base64FontExtractor {
  private static extractedFonts: Map<string, FontData> = new Map();

  static async extractFontFromCSS(cssPath: string): Promise<FontData | null> {
    try {
      const response = await fetch(cssPath);
      const cssText = await response.text();
      
      // Regex untuk mengekstrak font-family dan base64 data
      const fontFaceRegex = /@font-face\s*\{([^}]+)\}/g;
      const fontFamilyRegex = /font-family:\s*["']([^"']+)["'];/;
      const srcRegex = /src:\s*url\(data:([^;]+);base64,([^)]+)\)/;
      
      let match;
      while ((match = fontFaceRegex.exec(cssText)) !== null) {
        const fontFaceContent = match[1];
        
        const familyMatch = fontFamilyRegex.exec(fontFaceContent);
        const srcMatch = srcRegex.exec(fontFaceContent);
        
        if (familyMatch && srcMatch) {
          const fontFamily = familyMatch[1];
          const mimeType = srcMatch[1];
          const base64Data = srcMatch[2];
          
          // Tentukan format berdasarkan MIME type
          let format = 'truetype';
          if (mimeType.includes('opentype') || mimeType.includes('otf')) {
            format = 'opentype';
          }
          
          const fontData: FontData = {
            fontFamily,
            base64Data,
            format
          };
          
          this.extractedFonts.set(fontFamily, fontData);
          return fontData;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error extracting font from CSS:', error);
      return null;
    }
  }

  static async loadAllBase64Fonts(): Promise<Map<string, FontData>> {
    const fontFiles = [
      '/fonts/base64/MuseoSans-100.css',
      '/fonts/base64/MuseoSans-100Italic.css',
      '/fonts/base64/MuseoSans-300.css',
      '/fonts/base64/MuseoSans-300Italic.css',
      '/fonts/base64/MuseoSans-500.css',
      '/fonts/base64/MuseoSans-500Italic.css',
      '/fonts/base64/MuseoSans-700.css',
      '/fonts/base64/MuseoSans-700Italic.css',
      '/fonts/base64/MuseoSans-900.css',
      '/fonts/base64/MuseoSans-900Italic.css',
      '/fonts/base64/bookantiqua.css',
    ];

    const promises = fontFiles.map(file => this.extractFontFromCSS(file));
    await Promise.all(promises);
    
    return this.extractedFonts;
  }

  static getFontData(fontFamily: string): FontData | null {
    return this.extractedFonts.get(fontFamily) || null;
  }
}