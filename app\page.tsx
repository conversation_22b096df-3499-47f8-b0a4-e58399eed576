'use client';

import { useState, useEffect } from "react";
import { BookAdjusted } from "@/lib/database/books";
import HeroSection from "@/components/landingpage/HeroSection";
import BookGrid from "@/components/landingpage/BookGrid";
import FeaturesSection from "@/components/landingpage/FeaturesSection";
import CallToActionSection from "@/components/landingpage/CallToActionSection";
import Navbar from "@/components/navbar/Navbar";
import KidsPDFViewer from "@/components/KidsPdfViewer";

export default function HomePage() {
  const [books, setBooks] = useState<BookAdjusted[]>([]);
  const [selectedBook, setSelectedBook] = useState<BookAdjusted | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('id');
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  // Fetch books when component mounts
  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async () => {
    try {
      const response = await fetch('/api/books');
      if (!response.ok) {
        throw new Error('Failed to fetch books');
      }
      const data = await response.json();
      setBooks(data);
    } catch (err) {
      console.error('Error fetching books:', err);
    }
  };

  const handleStartReading = (bookId: number, language: string = 'id') => {
    // Find the book by ID
    const book = books.find(b => b.id === bookId);
    if (book) {
      setSelectedBook(book);
      setSelectedLanguage(language);
      setShowPDFViewer(true);
    }
  };

  const handleSubscription = (bookId: number) => {
    // Handle subscription logic here
    console.log('Subscription requested for book:', bookId);
    // You can implement subscription logic, e.g., redirect to payment page
  };

  const handleDownloadPreview = (bookId: number, language: string = 'id') => {
    // Handle preview download logic here
    const book = books.find(b => b.id === bookId);
    if (book) {
      let pdfUrl = book.originalPdfUrl;
      
      // Check if there's a translated version for the selected language
      if (language !== 'id' && book.languages) {
        const languageVersion = book.languages.find(lang => lang.language === language);
        pdfUrl = languageVersion?.translatedPdfUrl || book.originalPdfUrl;
      }
      
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `${book.namaBuku}-${language}-preview.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const scrollToBooks = () => {
    const booksSection = document.getElementById('books');
    if (booksSection) {
      booksSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  const getCurrentPdfUrl = () => {
    if (!selectedBook) return '';
    
    if (selectedLanguage === 'id') {
      return selectedBook.originalPdfUrl;
    }
    
    // Check if there's a translated version for the selected language
    if (selectedBook.languages) {
      const languageVersion = selectedBook.languages.find(lang => lang.language === selectedLanguage);
      return languageVersion?.translatedPdfUrl || selectedBook.originalPdfUrl;
    }
    
    return selectedBook.originalPdfUrl;
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-1">
        <HeroSection onCTA={scrollToBooks} ctaLink="" />
        <BookGrid 
          onBookAction={handleStartReading}
          onSubscription={handleSubscription}
          onDownloadPreview={handleDownloadPreview}
        />
        <FeaturesSection />
        <CallToActionSection onCTA={scrollToBooks} />
      </main>

      {/* PDF Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={getCurrentPdfUrl()}
          bookTitle={selectedBook.namaBuku}
          onClose={handleClosePDFViewer}
        />
      )}
    </div>
  );
}