{"name": "elib-web-ui-fixt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@lottiefiles/dotlottie-react": "^0.14.2", "@neondatabase/serverless": "^1.0.1", "@pdf-lib/fontkit": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "@types/pdfjs-dist": "^2.10.377", "@vercel/blob": "^1.1.1", "@vitalets/google-translate-api": "^9.2.1", "canvas": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.4.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "fabric": "^5.3.0", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.11.174", "react": "^19.0.0", "react-circle-flags": "^0.0.23", "react-dom": "^19.0.0", "sonner": "^2.0.6", "svg2pdf.js": "^2.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/fabric": "^5.3.10", "@types/google-translate-api": "^2.3.5", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "null-loader": "^4.0.1", "postcss": "^8.5.6", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.17", "typescript": "^5"}}