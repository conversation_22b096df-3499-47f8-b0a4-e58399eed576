import { pgTable, serial, varchar, text, integer, real, foreignKey, primaryKey } from 'drizzle-orm/pg-core';

export const books = pgTable('books', {
  id: serial('id').primaryKey(),
  namaBuku: varchar('nama_buku', { length: 255 }).notNull(),
  description: text('description'),
  category: varchar('category', { length: 50 }),
  pages: integer('pages'),
  license: varchar('license', { length: 50 }),
  author: varchar('author', { length: 100 }),
  harga: real('harga'),
  rating: real('rating'),
  coverImageUrl: text('cover_image_url'),
  originalPdfUrl: text('original_pdf_url'),
});

export const booksLanguages = pgTable('books_languages', {
  idBuku: integer('id_buku').notNull(),
  language: varchar('language', { length: 50 }).notNull(),
  translatedPdfUrl: text('translated_pdf_url'),
}, (table) => ({
  pk: primaryKey(table.idBuku, table.language),
  fk: foreignKey({
    columns: [table.idBuku],
    foreignColumns: [books.id],
    name: 'books_languages_id_buku_fkey',
  }).onDelete('cascade'),
}));

export type Book = typeof books.$inferSelect;
export type NewBook = typeof books.$inferInsert;
export type BookLanguage = typeof booksLanguages.$inferSelect;
export type NewBookLanguage = typeof booksLanguages.$inferInsert;