import React from 'react';
import Image from 'next/image';

export default function FeaturesSection() {
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-sky-50 to-cyan-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 font-quicksand"
          >
            Why KinderLib
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div 
              className="flex justify-center mb-4"
              role="img" 
              aria-label="Reading experience yang menarik"
            >
              <Image 
                src="/book.svg" 
                alt="Book icon" 
                width={64} 
                height={64}
                className="text-sky-500"
              />
            </div>
            <h3 
              className="text-2xl font-bold text-sky-600 mb-3 font-quicksand"
            >
              Reading is becoming more fun!
            </h3>
            <p 
              className="text-lg text-gray-600 font-quicksand"
            >
              Read anywhere and everywhere
            </p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div 
              className="flex justify-center mb-4"
              role="img" 
              aria-label="Langganan mudah"
            >
              <Image 
                src="/badge.svg" 
                alt="Badge icon" 
                width={64} 
                height={64}
                className="text-cyan-500"
              />
            </div>
            <h3 
              className="text-2xl font-bold text-cyan-600 mb-3 font-quicksand"
            >
              It is beyond easy
            </h3>
            <p 
              className="text-lg text-gray-600 font-quicksand"
            >
              With just a fingertip you could pick your favourite books
            </p>
          </div>
          
          <div className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div 
              className="flex justify-center mb-4"
              role="img" 
              aria-label="Tracking progress membaca"
            >
              <Image 
                src="/business.svg" 
                alt="Business icon" 
                width={64} 
                height={64}
                className="text-green-500"
              />
            </div>
            <h3 
              className="text-2xl font-bold text-green-600 mb-3 font-quicksand"
            >
              Trackable Progress everyday
            </h3>
            <p 
              className="text-lg text-gray-600 font-quicksand"
            >
              You will not loose your progress as it was monitored within the automated system
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}