import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Turn off `any` rule completely
      "@typescript-eslint/no-explicit-any": "off",

      // Warn instead of error, and ignore variables starting with _
      "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],

      // Disable missing useEffect dependencies warning globally
      "react-hooks/exhaustive-deps": "off",
    },
  },
];

export default eslintConfig;
