import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    poweredByHeader: false,
    images: {
        formats: ['image/webp','image/avif'],
        remotePatterns: [
            {
            protocol: 'https',
            hostname: '**',
            },
            {
            protocol: 'http',
            hostname: '**',
            },
        ],
    },
    // Enhanced webpack configuration for PDF.js (for non-Turbopack builds)
    webpack: (config, { isServer, nextRuntime }) => {
        // Handle canvas module for both server and client
        if (isServer || nextRuntime === "nodejs") {
            // For server-side, set canvas to false to avoid Node.js canvas requirement
            config.resolve.alias = {
                ...config.resolve.alias,
                canvas: false,
            };
        }

        // Add fallbacks for browser environment
        if (!isServer) {
            config.resolve.fallback = {
                ...config.resolve.fallback,
                canvas: false,
                encoding: false,
                fs: false,
                module: false,
                net: false,
                tls: false,
                crypto: false,
                stream: false,
                util: false,
                url: false,
                zlib: false,
                http: false,
                https: false,
                assert: false,
                os: false,
                path: false,
            };

            // Use null-loader for canvas in client-side builds to completely ignore it
            config.module.rules.push({
                test: /canvas/,
                use: 'null-loader',
            });
        }

        // Add externals for canvas
        config.externals = [...(config.externals || []), { canvas: "canvas" }];

        return config;
    },
    
    // Turbopack configuration for Next.js 15
    turbopack: {
        resolveAlias: {
            canvas: "./empty-module.js",
        },
    },
    
    // Add rewrites for PDF.js worker (optional - CDN is used in component)
    async rewrites() {
        return [
            {
                source: '/pdf.worker.js',
                destination: '/node_modules/pdfjs-dist/build/pdf.worker.min.js',
            },
        ];
    },
};

export default nextConfig;