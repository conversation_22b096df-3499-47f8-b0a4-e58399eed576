'use client';

import React, { useState, useEffect } from 'react';
import BookCard from '@/components/landingpage/BookCard';
import Pagination from '@/components/landingpage/Pagination';
import KidsPDFViewer from '@/components/KidsPdfViewer';
import { BookAdjusted } from '@/lib/database/books';

interface MyBooksProps {
  booksPerPage?: number;
  userId?: number; // Add userId prop to fetch user's owned books
}

export default function MyBooks({ booksPerPage = 3, userId }: MyBooksProps) {
  const [books, setBooks] = useState<BookAdjusted[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBook, setSelectedBook] = useState<BookAdjusted | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('id');
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  useEffect(() => {
    fetchMyBooks();
  }, [userId]);

  const fetchMyBooks = async () => {
    try {
      setLoading(true);
      // If you have a userId, fetch user's owned books
      // For now, we'll fetch all books and filter owned ones
      const response = await fetch(`/api/books`);
      if (!response.ok) {
        throw new Error('Failed to fetch your books');
      }
      const data = await response.json();
      setBooks(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate pagination values
  const totalPages = Math.ceil(books.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = books.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    document.getElementById('my-books')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleReadBook = (bookId: number, language: string = 'id') => {
    const book = books.find(b => b.id === bookId);
    if (book) {
      setSelectedBook(book);
      setSelectedLanguage(language);
      setShowPDFViewer(true);
    }
    console.log('Reading book ID:', bookId, 'Language:', language);
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  const handleDownloadBook = (bookId: number, language: string = 'id') => {
    const book = books.find(b => b.id === bookId);
    if (book) {
      let pdfUrl = book.originalPdfUrl;
      
      if (language !== 'id') {
        const languageVersion = book.languages.find(lang => lang.language === language);
        pdfUrl = languageVersion?.translatedPdfUrl || book.originalPdfUrl;
      }
      
      // Create a download link
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `${book.namaBuku}-${language}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    console.log('Downloading book ID:', bookId, 'Language:', language);
  };

  const getCurrentPdfUrl = () => {
    if (!selectedBook) return '';
    
    if (selectedLanguage === 'id') {
      return selectedBook.originalPdfUrl;
    }
    
    const languageVersion = selectedBook.languages.find(lang => lang.language === selectedLanguage);
    return languageVersion?.translatedPdfUrl || selectedBook.originalPdfUrl;
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            My Books
          </h1>
          <p className="text-lg text-gray-600 font-quicksand">
            Books Collection
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            My Books
          </h1>
          <p className="text-lg text-gray-600 font-quicksand">
            Books Collection
          </p>
        </div>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            My Books
          </h1>
          <p className="text-lg text-gray-600 font-quicksand">
            Books Collection ({books.length} {books.length === 1 ? 'book' : 'books'})
          </p>
        </div>

        {/* Books List */}
        <section id="my-books" className="space-y-6">
          {currentBooks.length > 0 ? (
            currentBooks.map((book) => (
              <BookCard 
                key={book.id} 
                book={book} 
                onStartReading={handleReadBook}
                onDownload={handleDownloadBook}
                isOwned={true}
              />
            ))
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2 font-quicksand">
                Your library is empty
              </h3>
              <p className="text-gray-600 font-quicksand mb-4">
                You haven&apos;t purchased any books yet. Start building your collection!
              </p>
              <a
                href="/books"
                className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-quicksand font-semibold"
              >
                Browse Books
              </a>
            </div>
          )}
        </section>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* PDF Viewer Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={getCurrentPdfUrl()}
          bookTitle={selectedBook.namaBuku}
          onClose={handleClosePDFViewer}
        />
      )}
    </>
  );
}