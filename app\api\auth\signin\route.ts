// File: app/api/auth/signin/route.ts
import { Pool } from '@neondatabase/serverless';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Connect to the Neon database
    const pool = new Pool({ connectionString: process.env.DATABASE_URL });

    // Query the database for the user
    const result = await pool.query(
      'SELECT id, username, email, password FROM users WHERE email = $1',
      [email]
    );

    // Check if user exists
    if (result.rows.length === 0) {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    const user = result.rows[0];

    // Compare passwords (plain text comparison - not recommended for production)
    if (user.password !== password) {
      return NextResponse.json(
        { message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Authentication successful
    // Don't return the password in the response
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      message: 'Sign in successful',
      user: userWithoutPassword
    });

  } catch (error) {
    console.error('Sign in error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}