import React, { useState, useRef, useEffect, useCallback } from 'react';

interface KidsPDFViewerProps {
  pdfUrl: string;
  bookTitle: string;
  onClose: () => void;
}

export default function KidsPDFViewer({ pdfUrl, bookTitle, onClose }: KidsPDFViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState<number>(1.0);
  const [pdfDoc, setPdfDoc] = useState<any>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load PDF.js from CDN
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
    script.onload = () => {
      // Set worker
      (window as any).pdfjsLib.GlobalWorkerOptions.workerSrc = 
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
      loadPDF();
    };
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [pdfUrl]);

  const loadPDF = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const fullPdfUrl = pdfUrl.startsWith('http') ? pdfUrl : `${window.location.origin}${pdfUrl}`;
      
      const loadingTask = (window as any).pdfjsLib.getDocument({
        url: fullPdfUrl,
        cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
        cMapPacked: true,
        standardFontDataUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/standard_fonts/',
      });
      
      const pdf = await loadingTask.promise;
      
      setPdfDoc(pdf);
      setNumPages(pdf.numPages);
      setLoading(false);
    } catch (err: any) {
      console.error('PDF load error:', err);
      setError(`Failed to load PDF: ${err.message}`);
      setLoading(false);
    }
  };

  const renderPage = useCallback(async (pageNum: number) => {
    if (!pdfDoc || !canvasRef.current || !containerRef.current) return;

    try {
      const page = await pdfDoc.getPage(pageNum);
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      const container = containerRef.current;
      
      if (!context) return;
      
      // Get device pixel ratio for sharp rendering
      const devicePixelRatio = window.devicePixelRatio || 1;
      
      // Get container dimensions
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      
      // Set canvas CSS size
      canvas.style.position = 'absolute';
      canvas.style.left = '0px';
      canvas.style.top = '0px';
      canvas.style.width = `${containerWidth}px`;
      canvas.style.height = `${containerHeight}px`;
      
      // Set canvas actual size (accounting for device pixel ratio)
      canvas.width = containerWidth * devicePixelRatio;
      canvas.height = containerHeight * devicePixelRatio;
      
      // Scale the context to match device pixel ratio
      context.scale(devicePixelRatio, devicePixelRatio);
      
      // Clear canvas with gray background
      context.fillStyle = '#374151'; // gray-700
      context.fillRect(0, 0, containerWidth, containerHeight);
      
      // Get page viewport with scale 1 first to get original dimensions
      const baseViewport = page.getViewport({ scale: 1 });
      
      // Calculate scale to fit container while maintaining aspect ratio
      const scaleX = containerWidth / baseViewport.width;
      const scaleY = containerHeight / baseViewport.height;
      const autoScale = Math.min(scaleX, scaleY);
      
      // Apply user zoom on top of auto-fit scale
      // Also multiply by devicePixelRatio for sharp rendering
      const finalScale = autoScale * scale * devicePixelRatio;
      
      // Get final viewport
      const viewport = page.getViewport({ scale: finalScale });
      
      // Calculate position to center the PDF content within the canvas
      // Divide by devicePixelRatio since we're working in CSS pixels for positioning
      const offsetX = (containerWidth - viewport.width / devicePixelRatio) / 2;
      const offsetY = (containerHeight - viewport.height / devicePixelRatio) / 2;
      
      // Create a temporary canvas for the PDF content
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = viewport.width;
      tempCanvas.height = viewport.height;
      const tempContext = tempCanvas.getContext('2d');
      
      if (!tempContext) return;
      
      const renderContext = {
        canvasContext: tempContext,
        viewport: viewport,
        background: 'white'
      };
      
      await page.render(renderContext).promise;
      
      // Draw the PDF content onto the main canvas, centered
      // Scale down the rendered content to account for the device pixel ratio
      context.drawImage(
        tempCanvas, 
        offsetX, 
        offsetY,
        viewport.width / devicePixelRatio,
        viewport.height / devicePixelRatio
      );
      
    } catch (err) {
      console.error('Page render error:', err);
    }
  }, [pdfDoc, scale]);

  useEffect(() => {
    if (pdfDoc && !loading) {
      renderPage(currentPage);
    }
  }, [pdfDoc, currentPage, scale, loading, renderPage]);

  // Re-render when container size changes
  useEffect(() => {
    const handleResize = () => {
      if (pdfDoc && !loading) {
        renderPage(currentPage);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [pdfDoc, currentPage, loading, renderPage]);

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const zoomIn = () => setScale(prev => Math.min(prev + 0.2, 3.0));
  const zoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.4));
  const resetZoom = () => setScale(1.0);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowLeft':
        goToPreviousPage();
        break;
      case 'ArrowRight':
        goToNextPage();
        break;
      case 'Escape':
        onClose();
        break;
      case '+':
      case '=':
        e.preventDefault();
        zoomIn();
        break;
      case '-':
        e.preventDefault();
        zoomOut();
        break;
      case '0':
        e.preventDefault();
        resetZoom();
        break;
    }
  }, [currentPage, numPages, onClose]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  const progressPercentage = numPages > 0 ? (currentPage / numPages) * 100 : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white shadow-lg">
        <h2 className="text-xl font-bold text-gray-900 truncate flex-1">
          {bookTitle}
        </h2>
        
        {/* Progress Bar */}
        <div className="flex items-center space-x-4 mx-8 flex-1">
          <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
            <div 
              className="bg-gradient-to-r from-blue-400 to-purple-500 h-6 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-semibold text-black drop-shadow-sm">
                {currentPage} / {numPages}
              </span>
            </div>
          </div>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={zoomOut}
            className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            title="Zoom out"
          >
            −
          </button>
          <span className="text-sm text-gray-600 min-w-[60px] text-center">
            {Math.round(scale * 100)}%
          </span>
          <button
            onClick={zoomIn}
            className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            title="Zoom in"
          >
            +
          </button>
          <button
            onClick={resetZoom}
            className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
            title="Reset zoom"
          >
            Reset
          </button>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors ml-4"
            aria-label="Close PDF viewer"
          >
            ×
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Left Navigation */}
        <div className="flex items-center justify-center w-16 bg-black bg-opacity-50">
          <button
            onClick={goToPreviousPage}
            disabled={currentPage === 1}
            className="w-12 h-12 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 disabled:bg-opacity-10 disabled:cursor-not-allowed text-white text-2xl font-bold flex items-center justify-center transition-all duration-200 hover:scale-110"
            title="Previous page"
          >
            ←
          </button>
        </div>

        {/* PDF Content */}
        <div 
          ref={containerRef}
          className="flex-1 relative bg-gray-700 overflow-hidden"
        >
          {loading && (
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent mb-4"></div>
              <span className="text-lg">Loading PDF...</span>
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center text-white">
              <div className="text-center">
                <div className="text-red-400 text-6xl mb-4">⚠️</div>
                <p className="text-red-400 text-xl mb-4">{error}</p>
                <button
                  onClick={loadPDF}
                  className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {!loading && !error && (
            <canvas
              ref={canvasRef}
              className="bg-white"
            />
          )}
        </div>

        {/* Right Navigation */}
        <div className="flex items-center justify-center w-16 bg-black bg-opacity-50">
          <button
            onClick={goToNextPage}
            disabled={currentPage === numPages}
            className="w-12 h-12 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 disabled:bg-opacity-10 disabled:cursor-not-allowed text-white text-2xl font-bold flex items-center justify-center transition-all duration-200 hover:scale-110"
            title="Next page"
          >
            →
          </button>
        </div>
      </div>

      {/* Footer with keyboard shortcuts */}
      <div className="p-2 bg-black bg-opacity-50 text-center">
        <div className="text-xs text-gray-300">
          <span className="inline-block mr-6">← → Navigate pages</span>
          <span className="inline-block mr-6">+ - Zoom</span>
          <span className="inline-block mr-6">0 Reset zoom</span>
          <span className="inline-block">ESC Close</span>
        </div>
      </div>
    </div>
  );
}