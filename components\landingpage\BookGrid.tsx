import React, { useState, useEffect } from 'react';
import BookCard from './BookCard';
import Pagination from './Pagination';
import { BookAdjusted } from '@/lib/database/books';

interface BookGridProps {
  books?: BookAdjusted[];
  onBookAction: (bookId: number, language?: string) => void;
  onSubscription?: (bookId: number) => void;
  onDownloadPreview?: (bookId: number, language?: string) => void;
}

export default function BookGrid({ 
  books: propBooks, 
  onBookAction, 
  onSubscription,
  onDownloadPreview 
}: BookGridProps) {
  const [books, setBooks] = useState<BookAdjusted[]>(propBooks || []);
  const [loading, setLoading] = useState(!propBooks);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const booksPerPage = 3;

  useEffect(() => {
    if (!propBooks) {
      fetchBooks();
    }
  }, [propBooks]);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/books');
      if (!response.ok) {
        throw new Error('Failed to fetch books');
      }
      const data = await response.json();
      setBooks(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate pagination values
  const totalPages = Math.ceil(books.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = books.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of books section when page changes
    document.getElementById('books')?.scrollIntoView({ behavior: 'smooth' });
  };

  if (loading) {
    return (
      <section id="books" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              What kind of book you are looking for?
            </h2>
            <p 
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              Kindly find and explore below.
            </p>
          </div>
          
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="books" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              What kind of book you are looking for?
            </h2>
            <p 
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              Kindly find and explore below.
            </p>
          </div>
          
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Error: {error}
          </div>
        </div>
      </section>
    );
  }

  if (books.length === 0) {
    return (
      <section id="books" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              What kind of book you are looking for?
            </h2>
            <p 
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              Kindly find and explore below.
            </p>
          </div>
          
          <div className="text-center">
            <div className="text-6xl mb-4">📖</div>
            <p 
              className="text-xl text-gray-600"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              No books available at the moment. Check back soon for new adventures!
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="books" className="min-h-screen w-full">
      <div className="w-full h-full p-4 md:p-8">
        <div className="text-center mb-8 md:mb-12">
          <h2 
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: 'Quicksand, sans-serif' }}
          >
            What kind of book you are looking for?
          </h2>
          <p 
            className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: 'Quicksand, sans-serif' }}
          >
            Kindly find and explore below.
          </p>
        </div>
        
        <div className="w-full space-y-6 md:space-y-8">
          {currentBooks.map((book) => (
            <BookCard 
              key={book.id} 
              book={book} 
              onStartReading={onBookAction}
              onSubscription={onSubscription}
              onDownloadPreview={onDownloadPreview}
              isOwned={false}
            />
          ))}
        </div>

        {/* Pagination Component */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mb-8"
          />
        )}
      </div>
    </section>
  );
}