import Link from 'next/link';
import Image from 'next/image';
import DotLottieAnimation from "./DotLottieAnimation";

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  ctaText?: string;
  ctaLink?: string;
  onCTA?: () => void;
}

export default function HeroSection({
  title = "It's story timee!",
  subtitle = "There are many interesting stories ready for you to read",
  description = "Reading is beyond fun",
  ctaText = "Start Reading Now",
  ctaLink = "#books",
  onCTA
}: HeroSectionProps) {
  return (
    <section className="min-h-[85vh] bg-gradient-to-br from-sky-100 to-cyan-50 flex items-center">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 items-center">
            {/* Left Illustration */}
            <div className="order-2 lg:order-1 flex justify-center">
              <div className="relative w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl aspect-square">
                {/* Book Stack Illustration */}
                <DotLottieAnimation
                    src="/lotties/kids-learn.lottie"
                    autoplay
                    loop
                    width={"100%"}
                    height={"100%"}
                />  
              </div>
            </div>

            {/* Right Text Content */}
            <div className="order-1 lg:order-2 text-center lg:text-left px-4 sm:px-6 lg:px-8 lg:pr-12">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 mb-2 sm:mb-2 leading-tight tracking-wide font-quicksand">
                {title}
              </h1>
              
              <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-sky-600 mb-2 sm:mb-2 font-quicksand">
                {subtitle}
              </h2>
              
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 mb-8 sm:mb-10 leading-relaxed max-w-xl lg:max-w-2xl mx-auto lg:mx-0 font-medium font-quicksand">
                {description}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 justify-center lg:justify-start">
                {ctaLink && !onCTA ? (
                  <Link href={ctaLink}>
                    <button 
                      className="relative w-[240px] h-[60px] mx-auto lg:mx-0"
                      aria-label={`${ctaText} - Temukan cerita seru untuk dibaca`}
                    >
                      <Image
                        src="/buttons/mulai-membaca.svg"
                        alt=""
                        width={240}
                        height={60}
                        className="absolute inset-0"
                      />
                      <span className="absolute left-3 right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg lg:text-xl">
                        {ctaText}
                      </span>
                    </button>
                  </Link>
                ) : (
                  <button 
                    onClick={onCTA}
                    className="relative w-[240px] h-[60px] mx-auto lg:mx-0"
                    aria-label={`${ctaText} - Temukan cerita seru untuk dibaca`}
                  >
                    <Image
                      src="/buttons/mulai-membaca.svg"
                      alt=""
                      width={240}
                      height={60}
                      className="absolute inset-0"
                    />
                    <span className="absolute left-3 right-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg lg:text-xl">
                      {ctaText}
                    </span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}