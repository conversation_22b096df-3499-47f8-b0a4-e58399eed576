import React from 'react';
import <PERSON><PERSON><PERSON>ieAnimation from './DotLottieAnimation';
import PoweredByTerang from './PoweredByTerang';

interface CallToActionSectionProps {
  onCTA: () => void;
}

export default function CallToActionSection({ onCTA }: CallToActionSectionProps) {
  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-sky-500 to-cyan-600">
      <div className="max-w-5xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left mb-10">
            <h2 
              className="text-4xl md:text-5xl font-bold text-white mb-6 font-quicksand"
            >
              Are you ready?
            </h2>
            
            {/* Mobile Animation - Shows only on mobile after h2 */}
            <div className="flex justify-center mb-8 lg:hidden">
              <div className="relative w-48 h-48 sm:w-56 sm:h-56">
                <DotLottieAnimation
                    src="/lotties/curiosity.lottie"
                    autoplay
                    loop
                    width={"100%"}
                    height={"100%"}
                />  
              </div>
            </div>
            
            <p 
              className="text-xl md:text-2xl text-sky-100 mb-8 font-quicksand"
            >
              Find your first book and start to read!
            </p>
            
            <button 
              onClick={onCTA}
              className="bg-white text-sky-600 hover:bg-sky-50 px-10 py-6 text-xl font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-sky-300 font-quicksand"
              aria-label="Pergi ke bagian buku untuk mulai membaca"
            >
              Pick my books
            </button>
          </div>
          
          {/* Right Animation - Shows only on desktop */}
          <div className="hidden lg:flex justify-left lg:justify-start">
            <div className="relative w-150 h-150 lg:w-150 lg:h-150">
              <DotLottieAnimation
                  src="/lotties/curiosity.lottie"
                  autoplay
                  loop
                  width={"500px"}
                  height={"500px"}
              />  
            </div>
          </div>
        </div>
      </div>
      <PoweredByTerang />
    </section>
  );
}