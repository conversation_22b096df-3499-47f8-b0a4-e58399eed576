// lib/database/books.ts
import { Pool } from '@neondatabase/serverless';

const pool = new Pool({ connectionString: process.env.DATABASE_URL });

export interface BookAdjusted {
  id: number;
  namaBuku: string;
  description: string;
  pages: number;
  license: string;
  author: string;
  harga: number;
  rating: number;
  coverImageUrl: string;
  originalPdfUrl: string;
  category: string;
  languages: BookLanguageAdjusted[];
}

export interface BookLanguageAdjusted {
  language: string;
  translatedPdfUrl: string;
}

export interface Book {
  id: number;
  nama_buku: string;
  description: string;
  pages: number;
  license: string;
  author: string;
  harga: number;
  rating: number;
  cover_image_url: string;
  original_pdf_url: string;
  category: string;
  languages: BookLanguage[];
}

export interface BookLanguage {
  language: string;
  translated_pdf_url: string;
}

export async function fetchBooks(): Promise<Book[]> {
  const client = await pool.connect();
  
  try {
    // Fetch books with their languages
    const booksQuery = `
      SELECT 
        b.id,
        b.nama_buku,
        b.description,
        b.pages,
        b.license,
        b.author,
        b.harga,
        b.rating,
        b.cover_image_url,
        b.original_pdf_url,
        b.category,
        bl.language,
        bl.translated_pdf_url
      FROM books b
      LEFT JOIN books_languages bl ON b.id = bl.id_buku
      ORDER BY b.id, bl.language
    `;
    
    const result = await client.query(booksQuery);
    
    // Group books with their languages
    const booksMap = new Map<number, Book>();
    
    result.rows.forEach(row => {
      const bookId = row.id;
      
      if (!booksMap.has(bookId)) {
        booksMap.set(bookId, {
          id: row.id,
          nama_buku: row.nama_buku,
          description: row.description,
          pages: row.pages,
          license: row.license,
          author: row.author,
          harga: row.harga,
          rating: row.rating,
          cover_image_url: row.cover_image_url,
          original_pdf_url: row.original_pdf_url,
          category: row.category,
          languages: []
        });
      }
      
      // Add language if it exists
      if (row.language && row.translated_pdf_url) {
        booksMap.get(bookId)!.languages.push({
          language: row.language,
          translated_pdf_url: row.translated_pdf_url
        });
      }
    });
    
    return Array.from(booksMap.values());
  } finally {
    client.release();
  }
}

export async function fetchBookById(bookId: number): Promise<Book | null> {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT 
        b.id,
        b.nama_buku,
        b.description,
        b.pages,
        b.license,
        b.author,
        b.harga,
        b.rating,
        b.cover_image_url,
        b.original_pdf_url,
        b.category,
        bl.language,
        bl.translated_pdf_url
      FROM books b
      LEFT JOIN books_languages bl ON b.id = bl.id_buku
      WHERE b.id = $1
      ORDER BY bl.language
    `;
    
    const result = await client.query(query, [bookId]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    const firstRow = result.rows[0];
    const book: Book = {
      id: firstRow.id,
      nama_buku: firstRow.nama_buku,
      description: firstRow.description,
      pages: firstRow.pages,
      license: firstRow.license,
      author: firstRow.author,
      harga: firstRow.harga,
      rating: firstRow.rating,
      cover_image_url: firstRow.cover_image_url,
      original_pdf_url: firstRow.original_pdf_url,
      category: firstRow.category,
      languages: []
    };
    
    // Add all languages
    result.rows.forEach(row => {
      if (row.language && row.translated_pdf_url) {
        book.languages.push({
          language: row.language,
          translated_pdf_url: row.translated_pdf_url
        });
      }
    });
    
    return book;
  } finally {
    client.release();
  }
}
