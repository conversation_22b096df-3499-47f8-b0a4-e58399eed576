"use client";
import React, { useEffect, useRef } from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

// Define the DotLottie player interface based on common methods
interface DotLottiePlayer {
  resize: () => void;
  play?: () => void;
  pause?: () => void;
  stop?: () => void;
  setSpeed?: (speed: number) => void;
  setDirection?: (direction: number) => void;
  setLoop?: (loop: boolean) => void;
  // Add other methods as needed
}

type Props = {
  src: string;
  width?: string | number;
  height?: string | number;
  loop?: boolean;
  autoplay?: boolean;
  devicePixelRatio?: number;
};

const DotLottieAnimation: React.FC<Props> = ({
  src,
  width = '100%',
  height = '100%',
  loop = true,
  autoplay = true,
  devicePixelRatio,
}) => {
  const dotLottieRef = useRef<DotLottiePlayer | null>(null);
  const [dpr, setDpr] = React.useState(devicePixelRatio);

  useEffect(() => {
    // Set devicePixelRatio only on the client-side
    if (typeof window !== 'undefined') {
      setDpr(devicePixelRatio || window.devicePixelRatio || 2);
    }
  }, [devicePixelRatio]);

  const dotLottieRefCallback = (player: DotLottiePlayer | null) => {
    dotLottieRef.current = player;
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const resizeObserver = new ResizeObserver(() => {
        if (dotLottieRef.current) {
          dotLottieRef.current.resize();
        }
      });

      const container = document.querySelector('.l-c');
      if (container) {
        resizeObserver.observe(container);
      }

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  return (
    <div className="l-c" style={{ width, height }}>
      <DotLottieReact
        src={src}
        loop={loop}
        autoplay={autoplay}
        style={{ width: '100%', height: '100%' }}
        renderConfig={{ devicePixelRatio: dpr }}
        dotLottieRefCallback={dotLottieRefCallback}
      />
    </div>
  );
};

export default DotLottieAnimation;