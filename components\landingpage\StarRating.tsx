import React from 'react';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: number;
  showRating?: boolean;
}

export function StarRating({ 
  rating, 
  maxRating = 5, 
  size = 24,
  showRating = true 
}: StarRatingProps) {
  const stars = [];
  
  // Different hand-drawn star shapes for variety (like a child would draw)
  const starPaths = [
    // Star 1 - wobbly and imperfect
    "M12 1.5c0.2 0.1 0.8 0.3 1.2 0.8l2.8 5.9c0.1 0.3 0.4 0.2 0.7 0.1l6.2 0.7c0.5 0.1 0.3 0.4 0.1 0.7l-4.8 4.3c-0.2 0.2-0.1 0.5 0.1 0.7l1.3 6.8c0.1 0.4-0.2 0.3-0.5 0.1L12 17.2c-0.3-0.2-0.6-0.1-0.9 0.1l-6.1 3.4c-0.3 0.2-0.6 0.3-0.5-0.1l1.2-6.8c0.1-0.2 0-0.5-0.2-0.7L0.7 8.8c-0.2-0.3-0.4-0.6 0.1-0.7l6.3-0.7c0.3-0.1 0.5-0.3 0.6-0.1l2.9-5.9c0.4-0.5 1-0.7 1.4-0.9z",
    
    // Star 2 - slightly different wobble
    "M12 2.2c0.1 0 0.7 0.2 1.1 0.6l2.9 6.1c0.2 0.3 0.3 0.2 0.8 0.2l6.1 0.6c0.4 0.1 0.4 0.3 0.2 0.6l-4.7 4.5c-0.3 0.2-0.2 0.4 0 0.8l1.1 6.7c0.1 0.5-0.1 0.4-0.4 0.2L12 18.1c-0.4-0.2-0.5-0.2-0.8 0l-6.1 3.4c-0.4 0.2-0.5 0.3-0.4-0.2l1.2-6.7c0.1-0.4 0.2-0.6-0.1-0.8L0.9 9.3c-0.2-0.3-0.2-0.5 0.2-0.6l6.1-0.6c0.5 0 0.6 0.1 0.8-0.2l2.9-6.1c0.3-0.4 0.8-0.6 1.1-0.6z",
    
    // Star 3 - more irregular
    "M12 1.8c0.3 0.1 0.6 0.4 1.0 0.7l3.0 6.0c0.1 0.4 0.5 0.1 0.7 0.1l6.0 0.8c0.6 0.1 0.2 0.5 0.0 0.8l-4.9 4.2c-0.1 0.3-0.3 0.6 0.2 0.7l1.0 6.9c0.0 0.3-0.3 0.2-0.6 0.0L12 17.5c-0.2-0.1-0.7-0.1-0.9 0.1l-6.4 3.5c-0.3 0.2-0.6 0.3-0.6 0.0l1.0-6.9c0.5-0.1 0.3-0.4 0.2-0.7L0.4 8.3c-0.2-0.3-0.6-0.7 0.0-0.8l6.0-0.8c0.2 0 0.6 0.3 0.7-0.1l3.0-6.0c0.4-0.3 0.7-0.6 1.0-0.7z",
    
    // Star 4 - chunky and cute
    "M12 2.0c0.4 0.2 0.9 0.5 1.3 0.9l2.7 5.8c0.3 0.2 0.4 0.3 0.9 0.2l6.3 0.5c0.3 0.0 0.5 0.2 0.3 0.5l-4.6 4.6c-0.2 0.4-0.1 0.3 0.1 0.6l1.4 6.6c0.2 0.4-0.1 0.5-0.3 0.3L12 18.0c-0.5-0.3-0.4-0.3-0.8 0l-6.1 3.0c-0.2 0.2-0.5 0.1-0.3-0.3l1.4-6.6c0.2-0.3 0.3-0.2 0.1-0.6L1.7 9.9c-0.2-0.3 0.0-0.5 0.3-0.5l6.3-0.5c0.5 0.1 0.6 0.0 0.9-0.2l2.7-5.8c0.4-0.4 0.9-0.7 1.3-0.9z",
    
    // Star 5 - extra wobbly
    "M12 1.3c0.1 0.2 0.5 0.1 0.8 0.5l3.2 6.2c0.2 0.1 0.6 0.4 0.8 0.3l6.4 0.9c0.7 0.1 0.1 0.6-0.1 0.9l-5.1 4.1c-0.4 0.1-0.2 0.7 0.0 0.9l0.9 6.5c0.1 0.6-0.4 0.1-0.7-0.1L12 17.8c-0.1-0.4-0.8-0.2-1.0 0.1l-6.2 3.7c-0.3 0.2-0.8 0.5-0.7-0.1l0.9-6.5c0.2-0.2 0.4-0.8 0.0-0.9L0.0 9.0c-0.2-0.3-0.8-0.8-0.1-0.9l6.4-0.9c0.2 0.1 0.6-0.2 0.8-0.3l3.2-6.2c0.3-0.4 0.7-0.3 0.8-0.5z"
  ];
  
  for (let i = 1; i <= maxRating; i++) {
    const filled = i <= rating;
    const halfFilled = i - 0.5 === rating;
    
    // Pick a random star path for variety
    const pathIndex = (i - 1) % starPaths.length;
    const starPath = starPaths[pathIndex];
    
    // Add slight random rotation for more childlike feel
    const rotation = (i * 7) % 15 - 7; // Random rotation between -7 and 7 degrees
    
    stars.push(
      <svg
        key={i}
        width={size}
        height={size}
        viewBox="0 0 24 24"
        className="inline-block"
        style={{ transform: `rotate(${rotation}deg)` }}
      >
        <defs>
          <linearGradient id={`half-${i}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="50%" stopColor="#FFD700" />
            <stop offset="50%" stopColor="#FFB6C1" />
          </linearGradient>
        </defs>
        <path
          d={starPath}
          fill={
            filled 
              ? "#FFD700" // Bright golden yellow
              : halfFilled 
                ? `url(#half-${i})` 
                : "#FFB6C1" // Light pink for empty stars
          }
          stroke="#FF69B4" // Hot pink stroke
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
  
  return (
    <div className="flex items-center gap-1">
      <div className="flex items-center gap-0.5">
        {stars}
      </div>
      {showRating && (
        <span className="text-lg text-pink-600 ml-2 font-quicksand font-bold">
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
}