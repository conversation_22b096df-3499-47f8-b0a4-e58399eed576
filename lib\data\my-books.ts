import { Book } from "./books";

// Updated books data with ratings
export const myBooks: Book[] = [
  {
    id: 1,
    title: "Kejutan Rapor Sekolah",
    author: "<PERSON><PERSON> Kinder",
    price: 25000,
    coverImage: "/thumbnails/kejutan-rapor-sekolah.png",
    pdfPath: "/Kejutan rapor Sekolah_isi.pdf",
    description: "Cerita seru tentang Andi yang menunggu rapor sekolah dengan penuh harap. Akankah ia mendapat nilai bagus? Buku ini mengajarkan tentang usaha, harapan, dan kejutan manis yang menanti.",
    pages: 24,
    license: "Creative Commons BY-NC-SA 4.0",
    rating: 4.5
  },
  {
    id: 2,
    title: "Pengalaman Seru Murid Baru",
    author: "Terang Kinder",
    price: 25000,
    coverImage: "/thumbnails/pengalaman-seru-murid-baru.png",
    pdfPath: "/Pengalaman Seru Murid Baru_isi.pdf",
    description: "<PERSON><PERSON><PERSON> petualangan Maya di hari pertama sekolah! Dari gugup bertemu teman baru hingga menemukan hal-hal menarik di sekolah. Cerita yang sempurna untuk anak yang akan mulai sekolah.",
    pages: 28,
    license: "Creative Commons BY-NC-SA 4.0",
    rating: 5
  },
  {
    id: 3,
    title: "Pertunjukan Sulap Niko",
    author: "Terang Kinder",
    price: 25000,
    coverImage: "/thumbnails/pertunjukkan-sulap-niko.png",
    pdfPath: "/Pertunjukan Sulap Niko_isi.pdf",
    description: "Niko si pesulap cilik menghadapi tantangan besar! Bisakah ia berhasil memukau penonton dengan trik sulap barunya? Cerita penuh kejutan yang mengajarkan tentang percaya diri dan ketekunan.",
    pages: 32,
    license: "Creative Commons BY-NC-SA 4.0",
    rating: 4
  },
];
