// File: components/sidebar-layout.tsx
'use client';
import React, { ReactNode } from 'react';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AppSidebar } from './AppSidebar';
import { usePathname } from 'next/navigation'; 

interface SidebarLayoutProps {
  children: ReactNode;
}

export default function SidebarLayout({ children }: SidebarLayoutProps) {
  const pathname = usePathname();
  const applyPadding = pathname.includes('admin');

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <h2 className="font-semibold">{applyPadding ? 'AdminPanel' : 'Reading Zone'}</h2>
        </header>
        <div className={`flex flex-1 flex-col ${!applyPadding ? 'p-4 gap-4' : ''}`}>
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}