CREATE TABLE Books (
    ID SERIAL PRIMARY KEY,
    <PERSON><PERSON><PERSON><PERSON>uku VARCHAR(255) NOT NULL,
    Description TEXT,
    Category VARCHAR(50),
    Pages INTEGER,
    License VARCHAR(50),
    Author VARCHAR(100),
    Harga FLOAT,
    Rating FLOAT,
    Cover_Image_URL TEXT,
    Original_PDF_URL TEXT
);
CREATE TABLE Books_Languages (
    ID_Buku INTEGER REFERENCES Books(ID) ON DELETE CASCADE,
    Language VARCHAR(50) NOT NULL,
    Translated_PDF_URL TEXT,
    PRIMARY KEY (ID_Buku, Language)
);
CREATE TABLE users (
    ID SERIAL PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(50) NOT NULL
);