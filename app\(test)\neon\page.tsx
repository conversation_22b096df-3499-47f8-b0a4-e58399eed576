// File: app/page.tsx
import { Pool } from '@neondatabase/serverless';

export default function Page() {
  async function create(formData: FormData) {
    'use server';
    // Connect to the Neon database
    const pool = new Pool({ connectionString: process.env.DATABASE_URL });
    const username = formData.get('username') as string;
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;
    // Insert the comment from the form into the Postgres database using parameterized query
    await pool.query('INSERT INTO users (username,email,password) VALUES ($1,$2,$3)', [username,email,password]);
  }

  return (
    <form action={create}>
      <input type="text" placeholder="write a username" name="username" />
      <input type="text" placeholder="write a email" name="email" />
      <input type="text" placeholder="write a password" name="password" />
      <button type="submit">Submit</button>
    </form>
  );
}